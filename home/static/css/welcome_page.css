html {
    box-sizing: border-box;
}

*,
*:before,
*:after {
    box-sizing: inherit;
}

body {
    max-width: 960px;
    min-height: 100vh;
    margin: 0 auto;
    padding: 0 15px;
    color: #231f20;
    font-family: 'Helvetica Neue', 'Segoe UI', <PERSON><PERSON>, sans-serif;
    line-height: 1.25;
}

a {
    background-color: transparent;
    color: #308282;
    text-decoration: underline;
}

a:hover {
    color: #ea1b10;
}

h1,
h2,
h3,
h4,
h5,
p,
ul {
    padding: 0;
    margin: 0;
    font-weight: 400;
}

svg:not(:root) {
    overflow: hidden;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e6e6e6;
}

.logo {
    width: 150px;
    margin-inline-end: 20px;
}

.logo a {
    display: block;
}

.figure-logo {
    max-width: 150px;
    max-height: 55.1px;
}

.release-notes {
    font-size: 14px;
}

.main {
    padding: 40px 0;
    margin: 0 auto;
    text-align: center;
}

.figure-space {
    max-width: 265px;
}

@keyframes pos {
    0%, 100% {
        transform: rotate(-6deg);
    }
    50% {
        transform: rotate(6deg);
    }
}

.egg {
    fill: #43b1b0;
    animation: pos 3s ease infinite;
    transform: translateY(50px);
    transform-origin: 50% 80%;
}

.main-text {
    max-width: 400px;
    margin: 5px auto;
}

.main-text h1 {
    font-size: 22px;
}

.main-text p {
    margin: 15px auto 0;
}

.footer {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    border-top: 1px solid #e6e6e6;
    padding: 10px;
}

.option {
    display: block;
    padding: 10px 10px 10px 34px;
    position: relative;
    text-decoration: none;
}

.option svg {
    width: 24px;
    height: 24px;
    fill: gray;
    border: 1px solid #d9d9d9;
    padding: 5px;
    border-radius: 100%;
    top: 10px;
    inset-inline-start: 0;
    position: absolute;
}

.option h2 {
    font-size: 19px;
    text-decoration: underline;
}

.option p {
    padding-top: 3px;
    color: #231f20;
    font-size: 15px;
    font-weight: 300;
}

@media (max-width: 996px) {
    body {
        max-width: 780px;
    }
}

@media (max-width: 767px) {
    .option {
        flex: 0 0 50%;
    }
}

@media (max-width: 599px) {
    .main {
        padding: 20px 0;
    }

    .figure-space {
        max-width: 200px;
    }

    .footer {
        display: block;
        width: 300px;
        margin: 0 auto;
    }
}

@media (max-width: 360px) {
    .header-link {
        max-width: 100px;
    }
}
